# Troubleshooting Guide
## SCE Portal Common Issues & Solutions

This guide covers common problems encountered during development and their solutions.

## 🚨 Quick Fixes

### Port Already in Use
**Problem**: `Error: listen EADDRINUSE: address already in use :::3000`

**Solutions**:
```bash
# Option 1: Kill process on port 3000
npx kill-port 3000

# Option 2: Find and kill process manually
lsof -i :3000                   # Find process ID
kill -9 <PID>                   # Kill specific process

# Option 3: Use different port
pnpm dev -- -p 3001
```

### Environment Variables Not Loading
**Problem**: Authentication fails, API calls return errors

**Solutions**:
```bash
# 1. Check file location
ls -la .env.local               # Should be in project root

# 2. Verify file contents
cat .env.local                  # Check for typos

# 3. Restart development server
# Stop server (Ctrl+C) and run:
pnpm dev

# 4. Check environment in browser
console.log(process.env.NEXTAUTH_URL)  # Should show value
```

### Git Attribution Issues
**Problem**: Commits showing wrong author

**Solutions**:
```bash
# Check current attribution
git log --format=fuller -n 3

# Fix last commit
git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit --amend --reset-author --no-edit

# Force push corrected commit
git push --force origin branch-name

# Verify environment variables
echo $AI_AGENT_NAME
echo $AI_AGENT_EMAIL
```

## 🔐 Authentication Issues

### OAuth Provider Errors

#### Google OAuth Failures
**Problem**: `Error 400: redirect_uri_mismatch`

**Solutions**:
```bash
# 1. Check Google Cloud Console settings
# Authorized redirect URIs should include:
# http://localhost:3000/api/auth/callback/google

# 2. Verify NEXTAUTH_URL
echo $NEXTAUTH_URL              # Should match your dev server

# 3. Check for port changes
# If dev server uses different port, update NEXTAUTH_URL:
NEXTAUTH_URL=http://localhost:3001
```

#### GitHub OAuth Failures
**Problem**: `Error: The redirect_uri MUST match the registered callback URL`

**Solutions**:
```bash
# 1. Check GitHub OAuth App settings
# Authorization callback URL should be:
# http://localhost:3000/api/auth/callback/github

# 2. Verify environment variables
echo $GITHUB_CLIENT_ID
echo $GITHUB_CLIENT_SECRET

# 3. Test OAuth app configuration
curl "https://github.com/login/oauth/authorize?client_id=${GITHUB_CLIENT_ID}&redirect_uri=http://localhost:3000/api/auth/callback/github"
```

### NextAuth Configuration Issues

#### Invalid NEXTAUTH_SECRET
**Problem**: Session creation fails, authentication doesn't persist

**Solutions**:
```bash
# Generate new secret
openssl rand -base64 32

# Add to .env.local
NEXTAUTH_SECRET=your-new-secret-here

# Restart development server
pnpm dev
```

#### Session Not Persisting
**Problem**: User gets signed out on page refresh

**Solutions**:
```bash
# 1. Check browser cookies
# Open DevTools → Application → Cookies
# Look for next-auth.session-token

# 2. Verify NEXTAUTH_URL
# Must match exactly (including port)
NEXTAUTH_URL=http://localhost:3000

# 3. Clear browser data and test again
# Clear cookies and local storage
```

## 🔧 Development Issues

### Build Failures

#### TypeScript Errors
**Problem**: `Type error: Property 'x' does not exist on type 'y'`

**Solutions**:
```bash
# 1. Run type check
pnpm type-check

# 2. Check for missing types
pnpm add -D @types/package-name

# 3. Restart TypeScript server in IDE
# VS Code: Ctrl+Shift+P → "TypeScript: Restart TS Server"

# 4. Clear Next.js cache
rm -rf .next
pnpm build
```

#### ESLint Errors
**Problem**: Build fails due to linting errors

**Solutions**:
```bash
# 1. Check specific errors
pnpm lint

# 2. Auto-fix where possible
pnpm lint:fix

# 3. Disable specific rules (if necessary)
// eslint-disable-next-line @typescript-eslint/no-unused-vars

# 4. Update ESLint configuration
# Edit .eslintrc.json if needed
```

### Package Management Issues

#### pnpm Installation Failures
**Problem**: `ERR_PNPM_PEER_DEP_ISSUES`

**Solutions**:
```bash
# 1. Clear pnpm cache
pnpm store prune

# 2. Delete node_modules and reinstall
rm -rf node_modules pnpm-lock.yaml
pnpm install

# 3. Use legacy peer deps (if needed)
pnpm install --legacy-peer-deps

# 4. Check Node.js version
node --version                  # Should be 18+
```

#### Wrong Package Manager Used
**Problem**: `npm` or `yarn` used instead of `pnpm`

**Solutions**:
```bash
# Remove other lock files
rm -f package-lock.json yarn.lock

# Use pnpm exclusively
pnpm install
pnpm dev

# Add to .npmrc to enforce pnpm
echo "engine-strict=true" >> .npmrc
```

## 🌐 Network & API Issues

### API Route Failures
**Problem**: `/api/auth/*` routes return 500 errors

**Solutions**:
```bash
# 1. Check server logs
pnpm dev                        # Look for error messages

# 2. Verify API route files exist
ls -la pages/api/auth/          # Should contain [...nextauth].ts

# 3. Test API endpoints directly
curl http://localhost:3000/api/auth/providers
curl http://localhost:3000/api/auth/session

# 4. Check environment variables
printenv | grep NEXTAUTH
printenv | grep GOOGLE
printenv | grep GITHUB
```

### Email Provider Issues (Resend)
**Problem**: Magic link emails not sending

**Solutions**:
```bash
# 1. Verify Resend configuration
echo $RESEND_API_KEY
echo $RESEND_DOMAIN
echo $RESEND_SENDER

# 2. Test Resend API directly
curl -X POST 'https://api.resend.com/emails' \
  -H 'Authorization: Bearer YOUR_API_KEY' \
  -H 'Content-Type: application/json' \
  -d '{"from": "<EMAIL>", "to": "<EMAIL>", "subject": "Test", "html": "Test email"}'

# 3. Check domain verification
# Verify DNS records in Resend dashboard

# 4. Review email logs
# Check Resend dashboard for delivery status
```

## 🔄 Git & Version Control Issues

### Branch Synchronization Problems
**Problem**: Local branch out of sync with remote

**Solutions**:
```bash
# 1. Check branch status
git status
git log --oneline --graph --decorate

# 2. Sync with remote
git fetch origin
git pull origin main

# 3. Reset to remote state (if needed)
git reset --hard origin/main

# 4. Force push (use carefully)
git push --force origin branch-name
```

### Merge Conflicts
**Problem**: Git merge conflicts during pull/merge

**Solutions**:
```bash
# 1. Check conflict status
git status

# 2. View conflicted files
git diff --name-only --diff-filter=U

# 3. Resolve conflicts manually
# Edit files to resolve <<<<<<< ======= >>>>>>> markers

# 4. Mark as resolved and commit
git add .
git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit -m "resolve: merge conflicts"
```

### Remote Repository Issues
**Problem**: `Permission denied` or authentication failures

**Solutions**:
```bash
# 1. Check remote URL
git remote -v

# 2. Verify GitHub authentication
echo $AI_AGENT_PAT               # Should show token

# 3. Test GitHub API access
curl -H "Authorization: token $AI_AGENT_PAT" https://api.github.com/user

# 4. Update remote URL if needed
git remote set-url origin https://github.com/stcloudenterprises/sce_portal.git
```

## 🖥️ Environment-Specific Issues

### Windows PowerShell Issues
**Problem**: Commands fail due to execution policy

**Solutions**:
```bash
# Use direct pnpm commands (not PowerShell cmdlets)
pnpm dev                        # ✅ Correct
Get-Process                     # ❌ Avoid PowerShell cmdlets

# Check execution policy (if needed)
Get-ExecutionPolicy

# Use cross-platform commands
ls                              # Instead of Get-ChildItem
pwd                             # Instead of Get-Location
```

### VS Code Integration Issues
**Problem**: TypeScript/ESLint not working in editor

**Solutions**:
```bash
# 1. Restart TypeScript server
# Ctrl+Shift+P → "TypeScript: Restart TS Server"

# 2. Reload VS Code window
# Ctrl+Shift+P → "Developer: Reload Window"

# 3. Check workspace settings
# Verify .vscode/settings.json configuration

# 4. Install required extensions
# - TypeScript and JavaScript Language Features
# - ESLint
# - Prettier
```

## 🧪 Testing Issues

### Test Failures
**Problem**: Tests fail unexpectedly

**Solutions**:
```bash
# 1. Run tests with verbose output
pnpm test -- --verbose

# 2. Run specific test file
pnpm test -- --testPathPattern="auth"

# 3. Clear test cache
pnpm test -- --clearCache

# 4. Check for async issues
pnpm test -- --detectOpenHandles

# 5. Update test snapshots (if needed)
pnpm test -- --updateSnapshot
```

### Authentication Testing Issues
**Problem**: Auth tests fail in CI/CD

**Solutions**:
```bash
# 1. Mock authentication in tests
// Mock NextAuth
jest.mock('next-auth/react')

# 2. Use test environment variables
# Create .env.test with test values

# 3. Skip auth tests in CI (if needed)
test.skip('auth flow', () => {
  // Test implementation
})
```

## 🚨 Emergency Procedures

### Complete Reset
**When everything is broken**:
```bash
# 1. Stash current changes
git stash

# 2. Reset to clean main
git checkout main
git reset --hard origin/main
git pull origin main

# 3. Clean dependencies
rm -rf node_modules .next pnpm-lock.yaml
pnpm install

# 4. Restart development
pnpm dev
```

### Security Incident Response
**If secrets are accidentally committed**:
```bash
# 1. IMMEDIATELY rotate exposed credentials
# - Change OAuth client secrets
# - Generate new API keys
# - Update production environment variables

# 2. Remove from Git history (if needed)
# See AI_AGENT_WORKFLOW.md Appendix B for git-filter-repo procedures

# 3. Force push cleaned history
git push --force --mirror origin

# 4. Coordinate with all team members
# Everyone must re-clone or clean their local repositories
```

## 📞 Getting Help

### Debug Information to Collect
```bash
# System information
node --version
pnpm --version
git --version

# Project status
git status
git log --oneline -n 5
pnpm list --depth=0

# Environment check
printenv | grep -E "(NEXT|AUTH|GOOGLE|GITHUB|RESEND|AI_AGENT)"
```

### Where to Find More Help
- **Project Documentation**: [AI Agent Workflow](AI_AGENT_WORKFLOW.md)
- **Setup Issues**: [Authentication Setup](AUTHENTICATION_SETUP.md)
- **Command Help**: [Command Reference](COMMAND_REFERENCE.md)
- **Next.js Issues**: [Next.js Documentation](https://nextjs.org/docs)
- **NextAuth Issues**: [NextAuth.js Documentation](https://next-auth.js.org/)

---

**Still having issues?** Document the problem with the debug information above and consult the [AI Agent Workflow](AI_AGENT_WORKFLOW.md) for comprehensive troubleshooting procedures.
