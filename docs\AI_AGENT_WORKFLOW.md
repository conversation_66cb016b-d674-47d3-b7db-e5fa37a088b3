# AI Agent & Human Reviewer Collaboration Workflow
## SCE Portal Project Documentation

### Table of Contents
1. [Overview](#overview)
2. [Authentication & Identity Management](#authentication--identity-management)
3. [Agent Types & Capabilities](#agent-types--capabilities)
4. [Development Workflow Process](#development-workflow-process)
5. [Git Operations & Attribution](#git-operations--attribution)
6. [Environment Variable Security](#environment-variable-security)
7. [Task Assignment & Tracking](#task-assignment--tracking)
8. [Quality Assurance & Testing](#quality-assurance--testing)
9. [Historical Context & Lessons Learned](#historical-context--lessons-learned)
10. [Troubleshooting Guide](#troubleshooting-guide)
11. [Future Reference & Migration](#future-reference--migration)

---

## Overview

This document outlines the collaborative workflow between AI agents and human reviewers for the SCE Portal project. Our approach balances automation efficiency with security best practices, enabling rapid development while maintaining code quality and protecting sensitive information.

### Key Principles
- **Security First**: No sensitive credentials in Git history
- **Clear Attribution**: Proper Git attribution for agent vs. human contributions
- **Hybrid Testing**: Local agents handle sensitive operations, remote agents focus on code changes
- **Structured Workflow**: Consistent branching and PR processes
- **Quality Assurance**: Comprehensive testing and review procedures

---

## Authentication & Identity Management

### Local Agent Authentication
Local agents authenticate using environment variables configured in `.env.local`:

```bash
# AI Agent Identity
AI_AGENT_NAME="[Agent Name]"
AI_AGENT_EMAIL="[Agent GitHub No-Reply Email]"
AI_AGENT_PAT="[GitHub Personal Access Token]" 

# Additional Configuration
GH_TOKEN="${AI_AGENT_PAT}"  # For GitHub API tool
```

### Remote Agent Authentication
Remote agents use GitHub OAuth integration connection configured in the Augment Settings, Tools section showing different services, not environment variables. This provides:
- Seamless integration with development environment
- Automatic token management
- Reduced configuration complexity
- Enhanced security through OAuth flows
- Service configured with agent credentials

### Identity Distinction
- **Agent Identity**: AI_AGENT_NAME - Used for all AI agent commits and operations
- **Reviewer Identity**: GITHUB_REVIEWER - Used for human reviewer operations and local Git configuration
- **Attribution**: Ensures clear tracking of who (human vs. AI) made specific changes

---

## Agent Types & Capabilities

### Local Agents
**Capabilities:**
- Full access to `.env.local` sensitive variables
- Complete API testing (OAuth, email, database connections)
- Real-time authentication flow testing
- Full integration testing capabilities
- Direct access to development environment

**Approval Requirements:**
- May require approval for PR creation
- Confirmation needed before committing changes
- Human review of sensitive operations

**Best Use Cases:**
- OAuth provider integration
- Email functionality (magic links, notifications)
- Database operations requiring real connections
- Third-party API integrations
- Payment processing
- Real-time features (WebSockets, etc.)

### Remote Agents
**Capabilities:**
- Template-based environment configuration
- Code changes and refactoring
- Build and compilation testing
- TypeScript/JavaScript logic implementation
- UI/UX development

**Limitations:**
- Cannot perform full API testing without secret injection
- Limited access to sensitive environment variables
- Restricted testing capabilities for authentication flows

**Best Use Cases:**
- UI/UX changes and styling
- Component development and refactoring
- TypeScript/JavaScript logic changes
- Build configuration updates
- Documentation updates
- Non-API functionality

---

## Development Workflow Process

### Standard Workflow
1. **Branch Creation**: Create new branch from updated main
2. **Development**: Complete feature implementation and testing
3. **Quality Assurance**: Run comprehensive test suite
4. **Pull Request**: Create PR for review and deployment
5. **Review & Merge**: Human reviewer validates and merges

### Local Agent Workflow
```bash
# 1. Trigger Recognition
# When user starts message with "New Task"

# 2. Pre-Task Setup (CRITICAL)
git checkout main
git pull origin main  # Fetch latest changes
git checkout -b feature/new-feature-name

# 3. Development Phase
# Complete implementation and testing

# 4. Pre-Commit Confirmation
# Confirm with peer before proceeding

# 5. Commit with Proper Attribution, overrides default credentials
git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit -m "feat: implement new feature"

# 6. Create Pull Request
git push origin feature/new-feature-name
# Create PR via GitHub API or web interface
```

### Remote Agent Workflow
Remote agents continue with their existing autonomous workflow:
- Automatic branch creation and management
- Independent development and testing (within limitations)
- Automatic PR creation upon completion
- Reviewer handles post-completion validation

---

## Git Operations & Attribution

### Commit Attribution
All AI agent commits must be properly attributed to maintain clear project history:

```bash
# Correct Agent Attribution
Author: Agent Name <<EMAIL>>
Committer: Agent Name <<EMAIL>>

# Command Template
git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit -m "message"
```

### Attribution Verification
Check commit attribution after agent operations:
```bash
# Verify attribution
git log --format=fuller

# Fix incorrect attribution
git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit --amend --reset-author --no-edit
git push --force origin <branch-name>
```

### Local Repository Configuration
- **Default Config**: Reviewer credentials for clean IDE experience
- **Agent Operations**: Temporary credential overrides via `-c` flags
- **No Permanent Changes**: Agent operations don't affect local Git configuration

---

## Environment Variable Security

### Hybrid Development/Production Approach
Our security model balances functionality with protection:

**Template Files (Repository)**
```bash
# Template syntax for security
NEXTAUTH_SECRET="${NEXTAUTH_SECRET:-development-secret-change-in-production}"
GOOGLE_CLIENT_ID="${GOOGLE_CLIENT_ID:-your-google-client-id}"
GOOGLE_CLIENT_SECRET="${GOOGLE_CLIENT_SECRET:-your-google-client-secret}"
```

**Local Development (`.env.local`)**
```bash
# Actual values for full functionality (gitignored)
NEXTAUTH_SECRET="actual-secret-value"
GOOGLE_CLIENT_ID="actual-google-client-id"
GOOGLE_CLIENT_SECRET="actual-google-client-secret"
```

### Security Benefits
- ✅ No sensitive credentials in Git history
- ✅ Templates show required variables without exposing values
- ✅ Portable across projects without value updates
- ✅ Production-ready approach

### Trade-offs
- ❌ Remote agents cannot perform full API testing without secrets
- ❌ Some functionality may be limited in remote agent environments
- ✅ Acceptable for code changes, builds, and non-API functionality

---

## Task Assignment & Tracking

### GitHub Integration
- **Issues**: Use GitHub Issues for task definition and tracking
- **Projects**: Leverage GitHub Projects for workflow management
- **Assignment**: Reference specific GitHub issue titles when assigning work to agents

### Task Assignment Process
1. **Create Issue**: Define task requirements and acceptance criteria
2. **Agent Assignment**: Reference issue in agent instructions
3. **Branch Creation**: Agent creates feature branch linked to issue
4. **Development**: Agent implements solution according to issue requirements
5. **PR Creation**: Link PR to original issue for tracking
6. **Review & Close**: Human reviewer validates and closes issue upon merge

### "New Task" Trigger Protocol
For local agents, the phrase "New Task" at the beginning of a message triggers:
1. Immediate branch creation from updated main
2. Proper Git setup and attribution configuration
3. Task-focused development workflow initiation

---

## Quality Assurance & Testing

### Post Task Actions (Required)
After completing any development task, run the following commands:

```bash
# Dependency Management
pnpm install

# Code Quality
pnpm lint          # ESLint error resolution
pnpm type-check    # TypeScript validation

# Testing
pnpm test          # Complete test suite

# Build Verification
pnpm build         # Production build validation
pnpm start         # Production build testing
```

### Remote Agent Post-Completion Checklist
When a remote agent completes a task, the reviewer must perform:

#### 1. API Testing (if applicable)
- [ ] Pull branch locally: `git checkout <branch-name>`
- [ ] Install dependencies: `pnpm install`
- [ ] Run development server: `pnpm dev`
- [ ] Test API endpoints requiring authentication
- [ ] Verify email functionality
- [ ] Test authentication flows

#### 2. Attribution Verification
- [ ] Check commit attribution: `git log --format=fuller`
- [ ] Verify proper agent attribution
- [ ] Fix attribution if incorrect

#### 3. Environment Variable Validation
- [ ] Ensure no hardcoded secrets committed
- [ ] Verify template syntax usage
- [ ] Confirm `.env.local` is gitignored
- [ ] Check Git history for sensitive data

#### 4. Full Integration Testing
- [ ] Run complete test suite: `pnpm test`
- [ ] Verify build success: `pnpm build`
- [ ] Test production build: `pnpm start`
- [ ] Check TypeScript errors: `pnpm type-check`
- [ ] Run linting: `pnpm lint`

#### 5. Security Review
- [ ] Review new environment variable usage
- [ ] Ensure no API keys in comments
- [ ] Verify proper error handling
- [ ] Check dependency sources

---

## Historical Context & Lessons Learned

### Authentication Evolution
**Challenge**: NextAuth configuration complexity and environment variable management
**Solution**: Implemented hybrid template approach with local `.env.local` files
**Lesson**: Balance security with development efficiency

### Git Attribution Issues
**Challenge**: Inconsistent commit attribution between agents and humans
**Solution**: Implemented temporary credential override system
**Lesson**: Clear attribution is essential for project maintenance

### Environment Variable Security
**Challenge**: Accidental exposure of sensitive credentials in Git history
**Solution**: Template-based approach with placeholder values
**Lesson**: Prevention is better than cleanup - implement security from the start

### Development Workflow Optimization
**Challenge**: Coordination between local and remote agents
**Solution**: Defined clear capabilities and use cases for each agent type
**Lesson**: Match agent capabilities to task requirements

### Testing Strategy
**Challenge**: Remote agents couldn't perform full integration testing
**Solution**: Hybrid approach with local validation for sensitive operations
**Lesson**: Security restrictions require workflow adaptations

---

## Troubleshooting Guide

### Common Issues & Solutions

#### 1. Commit Attribution Problems
**Symptom**: Commits showing wrong author/committer
**Solution**: 
```bash
git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit --amend --reset-author --no-edit
git push --force origin <branch-name>
```

#### 2. Environment Variable Issues
**Symptom**: API calls failing in development
**Solution**: Verify `.env.local` contains actual values, not template placeholders

#### 3. Branch Synchronization
**Symptom**: Missing recent changes in new branches
**Solution**: Always pull latest main before creating new branches
```bash
git checkout main
git pull origin main
git checkout -b new-feature-branch
```

#### 4. Remote Agent Testing Limitations
**Symptom**: Remote agent cannot test authentication flows
**Solution**: Use remote agents for code changes, local agents for API testing

#### 5. Augment Approval Prompting
**Symptom**: Excessive approval prompts for every operation
**Solution**: Enable "Auto run without approval prompting" in the Augment prompt panel

---

## Future Reference & Migration

### Onboarding New Team Members
This documentation serves as comprehensive onboarding material for:
- New developers joining the project
- Additional AI agents being configured
- Project stakeholders understanding the workflow
- Future project maintainers

### GitHub Documentation Migration
This document is prepared for migration to official GitHub project documentation:
- Structured for GitHub Wiki or docs folder
- Includes all necessary context for standalone reference
- Formatted for easy maintenance and updates
- Contains historical knowledge for long-term value

### Long-term Project Knowledge
Key information preserved for ongoing maintenance:
- Authentication provider configurations and lessons learned
- UI/UX decisions and their rationales
- Security practices and their evolution
- Workflow optimizations and their benefits
- Technical debt and future improvement opportunities

### Future Reminders
- **October 2025**: Revisit Auth.js v5 for WebAuthn/Passkey implementation
- **Ongoing**: Monitor and update security practices as project evolves
- **Quarterly**: Review and update workflow documentation based on new learnings

---

## Appendix A: Command Reference

### Essential Development Commands
```bash
# Package Management (use pnpm, not npm/yarn)
pnpm install                  # Install dependencies
pnpm dev                      # Start development server
pnpm build                    # Build for production
pnpm start                    # Start production server
pnpm test                     # Run test suite
pnpm lint                     # Run ESLint
pnpm type-check               # TypeScript validation

# Git Operations with Agent Attribution
git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit -m "message"
git -c user.name="${AI_AGENT_NAME}" -c user.email="${AI_AGENT_EMAIL}" commit --amend --reset-author --no-edit

# Branch Management
git checkout main
git pull origin main
git checkout -b feature/branch-name
git push origin feature/branch-name

# Attribution Verification
git log --format=fuller
git log --oneline --graph --decorate
```

### Environment Variable Templates
```bash
# NextAuth Configuration
NEXTAUTH_URL="${NEXTAUTH_URL:-http://localhost:3000}"
NEXTAUTH_SECRET="${NEXTAUTH_SECRET:-development-secret-change-in-production}"

# OAuth Providers
GOOGLE_CLIENT_ID="${GOOGLE_CLIENT_ID:-your-google-client-id}"
GOOGLE_CLIENT_SECRET="${GOOGLE_CLIENT_SECRET:-your-google-client-secret}"
GITHUB_CLIENT_ID="${GITHUB_CLIENT_ID:-your-github-client-id}"
GITHUB_CLIENT_SECRET="${GITHUB_CLIENT_SECRET:-your-github-client-secret}"

# Email Services
RESEND_API_KEY="${RESEND_API_KEY:-your-resend-api-key}"
RESEND_DOMAIN="${RESEND_DOMAIN:-your-domain.com}"
RESEND_SENDER="${RESEND_SENDER:-<EMAIL>}"

# AI Agent Configuration
AI_AGENT_NAME="${AI_AGENT_NAME:-Agent Name}"
AI_AGENT_EMAIL="${AI_AGENT_EMAIL:-<EMAIL>}"
AI_AGENT_PAT="${AI_AGENT_PAT:-your-github-personal-access-token}"
GH_TOKEN="${AI_AGENT_PAT}"
```

---

## Appendix B: Security Incident Response

### Git History Cleanup (Emergency Procedures)

#### When Cleanup is Required
- **Immediate Action**: If actual secrets (API keys, tokens, passwords) were committed
- **Priority Order**:
  1. Rotate/revoke exposed credentials FIRST
  2. Clean Git history SECOND
  3. Coordinate with all collaborators

#### Recommended Tool: git-filter-repo
GitHub officially recommends `git-filter-repo` over older tools:

```bash
# Installation
brew install git-filter-repo  # macOS
pip install git-filter-repo   # Cross-platform

# Remove specific file from all history
git-filter-repo --sensitive-data-removal --invert-paths --path .env.local

# Replace text patterns from all files
git-filter-repo --sensitive-data-removal --replace-text ../passwords.txt
```

#### Complete Cleanup Process
1. **Install git-filter-repo** (version 2.47+ with `--sensitive-data-removal` flag)
2. **Clone repository locally** for cleanup operations
3. **Run git-filter-repo** to remove sensitive data
4. **Check affected PRs**: `grep -c '^refs/pull/.*/head$' .git/filter-repo/changed-refs`
5. **Force push changes**: `git push --force --mirror origin`
6. **Contact GitHub Support** to remove cached views and PR references
7. **Coordinate with collaborators** to clean their local clones

#### Important Considerations
- **⚠️ High Risk**: Easy to recontaminate if collaborators have old clones
- **⚠️ Breaking Changes**: Commit hashes change, breaking tooling/automation
- **⚠️ Lost Data**: PR diffs become unavailable, signatures removed
- **⚠️ Coordination**: All team members must clean their clones or re-clone

#### Alternative: Accept the Risk
For development-only secrets in learning projects:
- Rotate the exposed credentials immediately
- Document the incident and lessons learned
- Implement better practices going forward
- Consider if history cleanup is worth the complexity

---

## Appendix C: Project-Specific Context

### SCE Portal Technology Stack
- **Framework**: Next.js 15.3.5 with App Router
- **Authentication**: NextAuth.js with Google OAuth
- **Styling**: Tailwind CSS
- **Package Manager**: pnpm (required, not npm/yarn)
- **Environment**: Windows command prompt sinc PowerShell has execution policy restrictions

### Authentication Providers Implemented
1. **Google OAuth**: Primary authentication method
2. **GitHub OAuth**: Secondary authentication option
3. **Email Magic Links**: Passwordless authentication (planned)
4. **WebAuthn/Passkeys**: Future implementation (Auth.js v5, October 2025+)

### UI/UX Design Decisions
- **Authentication Buttons**: Consistent styling with provider branding
- **Loading States**: 500ms delay to prevent flashing on fast connections
- **Responsive Design**: Different centering approaches per breakpoint
- **Icon Consistency**: Visual balance across different provider icons
- **State Management**: Reset functionality for incomplete OAuth flows

### Development Environment Specifics
- **Windows PowerShell**: Avoid PowerShell-specific commands due to execution policy restrictions
- **Command Buffering**: pnpm commands may appear to hang in read-process but execute successfully
- **Port Handling**: Dynamic port handling for development servers
- **Screen Captures**: Saved to `/screen_captures` folder (gitignored)

### Future Technical Debt
- **NextAuth v5 Migration**: Planned for October 2025+ when stable
- **WebAuthn Implementation**: Waiting for Auth.js v5 support
- **Email Provider Evaluation**: Resend vs Nodemailer cost analysis needed
- **Session System Optimization**: Current custom session system may need refinement

---

*This document is a living resource that should be updated as our workflow evolves and new lessons are learned. Regular reviews ensure it remains accurate and valuable for all team members.*

**Last Updated**: July 29, 2025
**Version**: 1.0
**Maintainer**: SCE Team Member
**Review Schedule**: Quarterly or after significant workflow changes
